# 前端监控页面开发计划（修订版）

## 📋 项目概况

### 技术栈
- **前端**: Vue 3 + TypeScript + Naive UI + Vite
- **后端**: FastAPI + Python
- **图表库**: ECharts 5.5.1（主要）、@visactor/vchart（特殊图表）
- **现有结构**: 已有系统管理模块 (`/manage`)、STRM任务系统、日志系统
- **依赖管理**: PDM + pyproject.toml
- **实时通信**: SSE-Starlette（已有依赖）

### 开发目标
创建独立的监控模块，重点监控fast-soy-admin应用程序的运行状态、业务指标和性能数据，附带基础的系统资源监控。充分利用现有的APILoggerMiddleware、STRM任务系统、日志系统等实际存在的组件。

## 🎯 功能需求

### 核心监控指标

#### 1. **应用健康监控**（主要）
- **应用状态**: 启动时间、运行时长、版本信息
- **数据库状态**: 连接状态、查询性能、数据库大小
- **API性能**: 基于APILoggerMiddleware的请求统计和响应时间

#### 2. **业务核心监控**（主要）
- **STRM任务统计**: 总数、成功率、失败率、处理速度（基于现有StrmTask模型）
- **用户活动**: 在线用户、登录统计、操作频率
- **文件处理**: 上传量、处理量、存储使用情况
- **API性能**: 请求量、响应时间、错误率统计（基于现有中间件）

#### 3. **系统资源监控**（可选）
- **应用资源**: 应用内存使用、CPU占用（需添加psutil依赖）
- **系统概览**: 系统内存、磁盘空间、负载
- **网络状态**: 连接数、流量统计

#### 4. **错误和异常监控**（主要）
- **错误统计**: 日志错误分析、异常趋势（基于现有日志系统）
- **失败任务**: STRM任务失败原因分析、重试统计
- **性能告警**: 响应时间异常、资源使用告警

### 页面功能
- **实时监控仪表板**: 关键指标实时更新
- **业务数据分析**: STRM任务、用户活动趋势图
- **性能监控**: API响应时间、数据库性能图表
- **健康状态检查**: 应用组件状态一览
- **历史数据查看**: 支持时间范围查询
- **告警和通知**: 异常状态提醒
- **响应式布局**: 适配不同设备
- **暗色模式**: 支持主题切换
- **数据导出**: 监控报告导出

## 📝 详细开发计划

### 第一阶段：后端API开发 (1-2天)

#### 1.1 安装监控依赖
```bash
# 在项目根目录执行
pdm add psutil  # 用于基础系统信息获取（可选）
pdm add apscheduler  # 用于定时任务（数据聚合）

# 或者直接编辑 pyproject.toml，在 dependencies 中添加：
# "psutil>=5.9.0",  # 系统资源监控（可选）
# "apscheduler>=3.10.0",  # 定时任务

# 项目已有的可用依赖：
# - tortoise-orm（数据库）
# - loguru（日志）
# - sse-starlette（实时通信）
# - APILoggerMiddleware（性能监控）- 已存在并可用
```

#### 1.2 创建监控API模块
**文件**: `app/api/v1/monitor.py`

**接口设计**:
```python
from fastapi import APIRouter, Depends
from app.core.dependency import get_current_user, DependPermission
from app.models.system import User, Log, APILog
from app.models.strm import StrmTask, TaskStatus
from app.schemas.base import Success
import platform
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from tortoise.functions import Count, Avg, Max, Min
from tortoise.expressions import Q
from tortoise import connections

# 可选的系统资源监控
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

router_monitor = APIRouter()

# 应用启动时间（需要在应用启动时设置）
app_start_time = datetime.now()

@router_monitor.get("/app-health", summary="获取应用健康状态")
async def get_app_health(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,  # 使用项目标准权限控制
):
    """获取应用健康状态（基于实际存在的组件）"""
    # 基于实际存在的组件重新设计
    pass

@router_monitor.get("/business-stats", summary="获取业务统计数据")
async def get_business_stats(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """获取业务统计（基于现有STRM任务和用户系统）"""
    # 基于现有StrmTask模型实现业务统计
    pass

@router_monitor.get("/performance", summary="获取性能监控数据")
async def get_performance_data(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """获取性能数据（基于现有APILoggerMiddleware收集的数据）"""
    # 利用现有APILog模型分析性能数据
    pass

@router_monitor.get("/system-overview", summary="获取系统资源概览")
async def get_system_overview(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """获取系统资源概览（可选功能，需要psutil）"""
    if not PSUTIL_AVAILABLE:
        return Success(data={"error": "系统资源监控功能未启用，请安装psutil依赖"})
    # 使用 psutil 获取系统资源信息
    pass

@router_monitor.get("/error-analysis", summary="获取错误分析数据")
async def get_error_analysis(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """获取错误分析（基于现有日志系统和STRM任务失败分析）"""
    # 分析STRM任务失败和API错误
    pass

@router_monitor.get("/realtime-stream", summary="实时监控数据流")
async def get_realtime_stream(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    """使用SSE提供实时监控数据流（带心跳机制）"""
    from sse_starlette.sse import EventSourceResponse
    import asyncio
    import json

    async def event_generator():
        last_heartbeat = datetime.now()
        heartbeat_interval = 15  # 15秒心跳间隔
        data_interval = 5       # 5秒数据更新间隔

        try:
            while True:
                current_time = datetime.now()

                # 发送心跳
                if (current_time - last_heartbeat).seconds >= heartbeat_interval:
                    yield {
                        "event": "heartbeat",
                        "data": json.dumps({
                            "timestamp": current_time.isoformat(),
                            "status": "alive"
                        })
                    }
                    last_heartbeat = current_time

                # 发送监控数据
                try:
                    monitor_data = {
                        "timestamp": current_time.isoformat(),
                        "type": "monitor_update",
                        "data": {
                            "app_health": await get_cached_app_health(),
                            "performance": await get_cached_performance_stats(minutes=1),
                            "business_stats": await get_cached_business_stats()
                        }
                    }

                    yield {
                        "event": "monitor_data",
                        "data": json.dumps(monitor_data)
                    }

                except Exception as e:
                    yield {
                        "event": "error",
                        "data": json.dumps({
                            "timestamp": current_time.isoformat(),
                            "error": str(e),
                            "type": "data_collection_error"
                        })
                    }

                await asyncio.sleep(data_interval)

        except asyncio.CancelledError:
            # 连接被取消时的清理
            yield {
                "event": "disconnect",
                "data": json.dumps({
                    "timestamp": datetime.now().isoformat(),
                    "message": "连接已断开"
                })
            }
            raise
        except Exception as e:
            yield {
                "event": "fatal_error",
                "data": json.dumps({
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e)
                })
            }

    return EventSourceResponse(event_generator())
```

#### 1.3 整合现有监控功能
```python
# 迁移现有的监控API到新模块
# 来源：app/api/v1/system_manage/apis.py

# 缓存管理器
from functools import lru_cache
from datetime import datetime, timedelta
import asyncio

class MonitorCache:
    """监控数据缓存管理器"""
    _cache = {}
    _cache_ttl = {}
    _locks = {}

    @classmethod
    async def get_or_set(cls, key: str, factory_func, ttl_seconds: int = 300):
        """获取缓存数据，如果不存在则调用工厂函数生成"""
        # 检查缓存是否有效
        if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
            return cls._cache[key]

        # 防止缓存击穿，使用锁确保同一时间只有一个请求去获取数据
        if key not in cls._locks:
            cls._locks[key] = asyncio.Lock()

        async with cls._locks[key]:
            # 双重检查，可能在等待锁的过程中其他请求已经更新了缓存
            if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
                return cls._cache[key]

            # 生成新数据
            data = await factory_func() if asyncio.iscoroutinefunction(factory_func) else factory_func()

            # 设置缓存
            cls._cache[key] = data
            cls._cache_ttl[key] = datetime.now() + timedelta(seconds=ttl_seconds)

            return data

    @classmethod
    def invalidate(cls, key: str):
        """使缓存失效"""
        cls._cache.pop(key, None)
        cls._cache_ttl.pop(key, None)

    @classmethod
    def clear_expired(cls):
        """清理过期缓存"""
        now = datetime.now()
        expired_keys = [key for key, ttl in cls._cache_ttl.items() if now >= ttl]
        for key in expired_keys:
            cls._cache.pop(key, None)
            cls._cache_ttl.pop(key, None)

async def get_cached_app_health():
    """获取缓存的应用健康状态（基于实际存在的组件）"""
    async def fetch_app_health():
        # 数据库连接检查
        db = connections.get("default")
        db_status = await check_database_health(db)

        # STRM任务健康状态（替代不存在的后台任务监控）
        strm_health = await get_strm_task_health()

        # API性能概览（基于现有APILoggerMiddleware）
        api_health = await get_api_health_summary()

        return {
            "app_info": {
                "name": "fast-soy-admin",
                "version": "1.0.0",  # 从配置获取
                "start_time": app_start_time.isoformat(),
                "uptime": int((datetime.now() - app_start_time).total_seconds()),
                "status": determine_overall_status(db_status, strm_health, api_health)
            },
            "database": db_status,
            "strm_tasks": strm_health,  # 替代background_tasks
            "api_performance": api_health  # 替代task_monitor
        }

    return await MonitorCache.get_or_set("app_health", fetch_app_health, ttl_seconds=30)

async def get_cached_business_stats():
    """获取缓存的业务统计数据（基于现有模型）"""
    async def fetch_business_stats():
        # 获取STRM任务统计（基于现有StrmTask模型）
        strm_stats = await StrmTask.annotate(
            total=Count('id'),
            completed=Count('id', _filter=Q(status=TaskStatus.COMPLETED)),
            failed=Count('id', _filter=Q(status=TaskStatus.FAILED)),
            running=Count('id', _filter=Q(status=TaskStatus.RUNNING)),
            pending=Count('id', _filter=Q(status=TaskStatus.PENDING))
        ).values('total', 'completed', 'failed', 'running', 'pending')

        # 获取用户活动统计（基于现有User模型）
        today = datetime.now().date()
        user_stats = await User.annotate(
            total_users=Count('id'),
            active_today=Count('id', _filter=Q(last_login__date=today))
        ).values('total_users', 'active_today')

        # 获取API请求统计（基于现有APILog模型）
        api_stats = await get_api_request_stats()

        return {
            "strm_tasks": strm_stats[0] if strm_stats else {},
            "user_activity": user_stats[0] if user_stats else {},
            "api_requests": api_stats,
            "timestamp": datetime.now().isoformat()
        }

    return await MonitorCache.get_or_set("business_stats", fetch_business_stats, ttl_seconds=60)
```

#### 1.4 数据模型设计
```python
# 应用健康状态模型（基于实际存在的组件重新设计）
AppHealthData = {
    "app_info": {
        "name": "fast-soy-admin",
        "version": str,
        "start_time": str,  # ISO格式时间字符串
        "uptime": int,  # 运行时长（秒）
        "status": "healthy|warning|error"
    },
    "database": {
        "status": "connected|disconnected",
        "size": int,  # 数据库大小（字节）
        "connection_pool": {
            "active": int,
            "idle": int,
            "total": int
        },
        "query_performance": {
            "avg_response_time": float,
            "slow_queries": int
        }
    },
    "strm_tasks": {  # 替代background_tasks
        "status": "healthy|warning|error",
        "total_tasks": int,
        "running_tasks": int,
        "failed_tasks": int,
        "success_rate": float,
        "recent_failures": int
    },
    "api_performance": {  # 替代task_monitor
        "status": "healthy|warning|error",
        "avg_response_time": float,
        "request_count_24h": int,
        "error_rate": float,
        "slowest_endpoints": list
    }
}

# 业务统计数据模型（基于现有STRM任务和用户系统）
BusinessStatsData = {
    "strm_tasks": {
        "total": int,
        "completed": int,
        "failed": int,
        "running": int,
        "pending": int,
        "success_rate": float,
        "avg_processing_time": float,
        "recent_tasks": List[Dict]  # 最近任务列表
    },
    "user_activity": {
        "total_users": int,
        "active_today": int,  # 今日活跃用户
        "login_count_today": int,
        "active_users_week": int
    },
    "api_requests": {  # 基于现有APILog模型
        "total_requests_today": int,
        "avg_response_time": float,
        "error_count": int,
        "top_endpoints": List[Dict]
    },
    "file_processing": {  # 基于STRM任务的文件处理统计
        "total_files_processed": int,
        "success_files": int,
        "failed_files": int,
        "avg_file_size": float,
        "storage_used": int  # 字节
    }
}

# 性能监控数据模型（基于现有APILoggerMiddleware）
PerformanceData = {
    "api_performance": {
        "avg_response_time": float,
        "request_count": int,
        "error_rate": float,
        "slowest_endpoints": List[Dict],
        "requests_per_minute": float,
        "status_code_distribution": Dict[str, int]  # HTTP状态码分布
    },
    "database_performance": {
        "connection_status": str,  # 连接状态
        "database_size": int,  # 数据库大小
        "query_stats": {
            "avg_query_time": float,  # 基于APILog的process_time
            "slow_queries": int,
            "total_queries": int
        }
    },
    "system_resources": {  # 可选，需要psutil
        "available": bool,  # 是否可用
        "memory_usage": float,  # 内存使用率
        "cpu_usage": float,  # CPU使用率
        "disk_usage": float  # 磁盘使用率
    }
}
```

#### 1.5 基于现有APILog的性能数据分析
```python
# 基于现有APILog模型的性能数据分析
# 文件：app/core/performance_analyzer.py

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from tortoise.functions import Count, Avg, Max, Min
from tortoise.expressions import Q
from app.models.system import APILog

class PerformanceAnalyzer:
    """基于现有APILog模型的性能分析器"""

    @staticmethod
    async def get_performance_stats(minutes: int = 60) -> Dict:
        """获取性能统计数据（基于APILog表）"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)

        # 获取时间范围内的API日志
        logs = await APILog.filter(
            create_time__gte=cutoff_time,
            process_time__isnull=False
        ).all()

        if not logs:
            return {"error": "没有最近的性能数据"}

        # 计算基础统计
        process_times = [log.process_time for log in logs if log.process_time]
        response_codes = [log.response_code for log in logs if log.response_code]

        # 按端点分组统计
        endpoint_stats = {}
        for log in logs:
            endpoint = f"{log.request_path}"
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = []
            if log.process_time:
                endpoint_stats[endpoint].append(log.process_time)

        return {
            "total_requests": len(logs),
            "avg_response_time": sum(process_times) / len(process_times) if process_times else 0,
            "max_response_time": max(process_times) if process_times else 0,
            "min_response_time": min(process_times) if process_times else 0,
            "error_rate": len([c for c in response_codes if c and int(c) >= 400]) / len(response_codes) if response_codes else 0,
            "requests_per_minute": len(logs) / minutes,
            "slowest_endpoints": PerformanceAnalyzer._get_slowest_endpoints(endpoint_stats),
            "status_code_distribution": PerformanceAnalyzer._get_status_distribution(response_codes)
        }

    @staticmethod
    def _get_slowest_endpoints(endpoint_stats: Dict, top_n: int = 5) -> List[Dict]:
        """获取最慢的端点"""
        endpoint_averages = []
        for endpoint, times in endpoint_stats.items():
            if times:
                endpoint_averages.append({
                    "endpoint": endpoint,
                    "avg_time": sum(times) / len(times),
                    "count": len(times),
                    "max_time": max(times)
                })
        return sorted(endpoint_averages, key=lambda x: x["avg_time"], reverse=True)[:top_n]

    @staticmethod
    def _get_status_distribution(response_codes: List) -> Dict[str, int]:
        """获取状态码分布"""
        distribution = {}
        for code in response_codes:
            if code:
                code_str = str(code)
                distribution[code_str] = distribution.get(code_str, 0) + 1
        return distribution

async def get_strm_task_health() -> Dict:
    """获取STRM任务健康状态"""
    from app.models.strm import StrmTask, TaskStatus

    # 获取最近24小时的任务
    recent_time = datetime.now() - timedelta(hours=24)
    recent_tasks = await StrmTask.filter(create_time__gte=recent_time).all()

    if not recent_tasks:
        return {
            "status": "healthy",
            "total_tasks": 0,
            "running_tasks": 0,
            "failed_tasks": 0,
            "success_rate": 1.0,
            "recent_failures": 0
        }

    running_count = len([t for t in recent_tasks if t.status == TaskStatus.RUNNING])
    failed_count = len([t for t in recent_tasks if t.status == TaskStatus.FAILED])
    completed_count = len([t for t in recent_tasks if t.status == TaskStatus.COMPLETED])

    success_rate = completed_count / len(recent_tasks) if recent_tasks else 1.0

    # 判断健康状态
    if failed_count > len(recent_tasks) * 0.3:  # 失败率超过30%
        status = "error"
    elif failed_count > len(recent_tasks) * 0.1:  # 失败率超过10%
        status = "warning"
    else:
        status = "healthy"

    return {
        "status": status,
        "total_tasks": len(recent_tasks),
        "running_tasks": running_count,
        "failed_tasks": failed_count,
        "success_rate": success_rate,
        "recent_failures": failed_count
    }

async def get_api_health_summary() -> Dict:
    """获取API健康状态摘要"""
    analyzer = PerformanceAnalyzer()
    stats = await analyzer.get_performance_stats(minutes=60)

    if "error" in stats:
        return {
            "status": "unknown",
            "avg_response_time": 0,
            "request_count_24h": 0,
            "error_rate": 0,
            "slowest_endpoints": []
        }

    # 判断API健康状态
    error_rate = stats.get("error_rate", 0)
    avg_response_time = stats.get("avg_response_time", 0)

    if error_rate > 0.1 or avg_response_time > 5:  # 错误率>10%或平均响应时间>5秒
        status = "error"
    elif error_rate > 0.05 or avg_response_time > 2:  # 错误率>5%或平均响应时间>2秒
        status = "warning"
    else:
        status = "healthy"

    return {
        "status": status,
        "avg_response_time": avg_response_time,
        "request_count_24h": stats.get("total_requests", 0),
        "error_rate": error_rate,
        "slowest_endpoints": stats.get("slowest_endpoints", [])
    }

async def check_database_health(db):
    """检查数据库健康状态"""
    try:
        # 测试数据库连接
        await db.execute_query("SELECT 1")

        # 获取数据库大小（SQLite）
        try:
            size_result = await db.execute_query(
                "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
            )
            db_size = size_result[0]["size"] if size_result else 0
        except:
            db_size = 0

        return {
            "status": "connected",
            "size": db_size,
            "connection_pool": {
                "active": 1,  # SQLite单连接
                "idle": 0,
                "total": 1
            },
            "query_performance": {
                "avg_response_time": 0.0,  # 可以后续基于APILog统计
                "slow_queries": 0
            }
        }
    except Exception as e:
        return {
            "status": "disconnected",
            "error": str(e),
            "size": 0,
            "connection_pool": {"active": 0, "idle": 0, "total": 0},
            "query_performance": {"avg_response_time": 0.0, "slow_queries": 0}
        }

async def get_api_request_stats() -> Dict:
    """获取API请求统计"""
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())

    # 获取今日API请求
    today_logs = await APILog.filter(create_time__gte=today_start).all()

    # 统计响应时间
    process_times = [log.process_time for log in today_logs if log.process_time]
    avg_response_time = sum(process_times) / len(process_times) if process_times else 0

    # 统计错误
    error_count = len([log for log in today_logs if log.response_code and int(log.response_code) >= 400])

    # 统计热门端点
    endpoint_counts = {}
    for log in today_logs:
        path = log.request_path
        endpoint_counts[path] = endpoint_counts.get(path, 0) + 1

    top_endpoints = sorted(endpoint_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    top_endpoints = [{"endpoint": ep, "count": count} for ep, count in top_endpoints]

    return {
        "total_requests_today": len(today_logs),
        "avg_response_time": avg_response_time,
        "error_count": error_count,
        "top_endpoints": top_endpoints
    }

async def get_cached_performance_stats(minutes: int = 5) -> Dict:
    """获取缓存的性能统计数据"""
    analyzer = PerformanceAnalyzer()
    return await analyzer.get_performance_stats(minutes)
```

#### 1.6 更新路由配置
**文件**: `app/api/v1/__init__.py`

在现有导入中添加：
```python
from fastapi import APIRouter

from .auth import router_auth
from .route import router_route
from .system_manage import router_system_manage
from .strm import router_strm
from .monitor import router_monitor  # 新增

v1_router = APIRouter()

v1_router.include_router(router_auth, prefix="/auth", tags=["权限认证"])
v1_router.include_router(router_route, prefix="/route", tags=["路由管理"])
v1_router.include_router(router_system_manage, prefix="/system-manage", tags=["系统管理"])
v1_router.include_router(router_strm, prefix="/strm", tags=["STRM 文件管理"])
v1_router.include_router(router_monitor, prefix="/monitor", tags=["系统监控"])  # 新增
```

#### 1.7 添加辅助函数和缓存机制
**文件**: `app/core/monitor_cache.py`

```python
from functools import lru_cache
from datetime import datetime, timedelta
import asyncio
from typing import Dict, Any, Callable

class MonitorCache:
    """监控数据缓存管理器（简化版）"""
    _cache = {}
    _cache_ttl = {}
    _locks = {}

    @classmethod
    async def get_or_set(cls, key: str, factory_func: Callable, ttl_seconds: int = 300):
        """获取缓存数据，如果不存在则调用工厂函数生成"""
        # 检查缓存是否有效
        if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
            return cls._cache[key]

        # 防止缓存击穿，使用锁确保同一时间只有一个请求去获取数据
        if key not in cls._locks:
            cls._locks[key] = asyncio.Lock()

        async with cls._locks[key]:
            # 双重检查，可能在等待锁的过程中其他请求已经更新了缓存
            if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
                return cls._cache[key]

            # 生成新数据
            data = await factory_func() if asyncio.iscoroutinefunction(factory_func) else factory_func()

            # 设置缓存
            cls._cache[key] = data
            cls._cache_ttl[key] = datetime.now() + timedelta(seconds=ttl_seconds)

            return data

    @classmethod
    def invalidate(cls, key: str):
        """使缓存失效"""
        cls._cache.pop(key, None)
        cls._cache_ttl.pop(key, None)

    @classmethod
    def clear_expired(cls):
        """清理过期缓存"""
        now = datetime.now()
        expired_keys = [key for key, ttl in cls._cache_ttl.items() if now >= ttl]
        for key in expired_keys:
            cls._cache.pop(key, None)
            cls._cache_ttl.pop(key, None)

def determine_overall_status(db_status: Dict, strm_health: Dict, api_health: Dict) -> str:
    """确定整体应用状态"""
    if db_status.get("status") == "disconnected":
        return "error"

    if strm_health.get("status") == "error" or api_health.get("status") == "error":
        return "error"

    if strm_health.get("status") == "warning" or api_health.get("status") == "warning":
        return "warning"

    return "healthy"
```

### 第二阶段：前端路由和页面结构 (1天)

#### 2.1 添加路由配置
**文件**: `web/src/router/elegant/routes.ts`

添加新的监控模块路由：
```typescript
{
  name: 'monitor',
  path: '/monitor',
  component: 'layout.base',
  meta: {
    title: 'monitor',
    i18nKey: 'route.monitor',
    icon: 'mdi:monitor-dashboard',
    order: 8,
    roles: ['R_ADMIN']
  },
  children: [
    {
      name: 'monitor_dashboard',
      path: '/monitor/dashboard',
      component: 'view.monitor_dashboard',
      meta: {
        title: 'monitor_dashboard',
        i18nKey: 'route.monitor_dashboard',
        icon: 'mdi:view-dashboard',
        order: 1
      }
    }
  ]
}
```

#### 2.2 创建页面目录结构
```
web/src/views/monitor/
├── dashboard/
│   ├── index.vue                    # 监控仪表板主页面
│   └── modules/
│       ├── app-health-overview.vue  # 应用健康状态概览
│       ├── business-stats.vue       # 业务统计图表
│       ├── strm-task-monitor.vue    # STRM任务监控
│       ├── user-activity.vue        # 用户活动监控
│       ├── performance-charts.vue   # 性能监控图表
│       ├── error-analysis.vue       # 错误分析
│       ├── system-resources.vue     # 系统资源监控
│       └── real-time-alerts.vue     # 实时告警
```

### 第三阶段：前端服务层开发 (1天)

#### 3.1 API服务接口
**文件**: `web/src/service/api/monitor.ts`

```typescript
import { request } from '../request';

/** 获取应用健康状态 */
export function fetchAppHealth() {
  return request<Api.Monitor.AppHealthData>({
    url: '/monitor/app-health',
    method: 'get'
  });
}

/** 获取业务统计数据 */
export function fetchBusinessStats() {
  return request<Api.Monitor.BusinessStatsData>({
    url: '/monitor/business-stats',
    method: 'get'
  });
}

/** 获取性能监控数据 */
export function fetchPerformanceData() {
  return request<Api.Monitor.PerformanceData>({
    url: '/monitor/performance',
    method: 'get'
  });
}

/** 获取系统资源概览 */
export function fetchSystemOverview() {
  return request<Api.Monitor.SystemOverviewData>({
    url: '/monitor/system-overview',
    method: 'get'
  });
}

/** 获取错误分析数据 */
export function fetchErrorAnalysis(params?: Api.Monitor.ErrorAnalysisParams) {
  return request<Api.Monitor.ErrorAnalysisData>({
    url: '/monitor/error-analysis',
    method: 'get',
    params
  });
}

/** 获取实时监控数据流 */
export function fetchRealtimeStream() {
  return request<Api.Monitor.RealtimeStreamData>({
    url: '/monitor/realtime-stream',
    method: 'get'
  });
}

/** 建立SSE连接获取实时数据（增强版，带心跳检测） */
export function createRealtimeConnection(onMessage: (data: any) => void, onError?: (error: any) => void) {
  let eventSource: EventSource | null = null;
  let heartbeatTimer: NodeJS.Timeout | null = null;
  let reconnectTimer: NodeJS.Timeout | null = null;
  let reconnectAttempts = 0;
  const maxReconnectAttempts = 5;
  const heartbeatTimeout = 30000; // 30秒没收到心跳就认为连接断开
  const reconnectDelay = 1000; // 重连延迟基数

  function connect() {
    try {
      eventSource = new EventSource('/api/v1/monitor/realtime-stream');

      // 重置心跳定时器
      resetHeartbeatTimer();

      eventSource.addEventListener('heartbeat', (event) => {
        // 收到心跳，重置定时器
        resetHeartbeatTimer();
        console.debug('收到心跳:', event.data);
      });

      eventSource.addEventListener('monitor_data', (event) => {
        resetHeartbeatTimer();
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('解析监控数据失败:', error);
        }
      });

      eventSource.addEventListener('error', (event) => {
        try {
          const errorData = JSON.parse(event.data);
          console.error('监控数据收集错误:', errorData);
          onError?.(errorData);
        } catch (error) {
          console.error('解析错误数据失败:', error);
        }
      });

      eventSource.onopen = () => {
        console.log('SSE连接已建立');
        reconnectAttempts = 0; // 重置重连计数
        resetHeartbeatTimer();
      };

      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error);
        cleanup();
        attemptReconnect();
      };

    } catch (error) {
      console.error('创建SSE连接失败:', error);
      attemptReconnect();
    }
  }

  function resetHeartbeatTimer() {
    if (heartbeatTimer) {
      clearTimeout(heartbeatTimer);
    }
    heartbeatTimer = setTimeout(() => {
      console.warn('心跳超时，连接可能已断开');
      cleanup();
      attemptReconnect();
    }, heartbeatTimeout);
  }

  function attemptReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连');
      onError?.({ type: 'max_reconnect_exceeded', message: '连接失败，请刷新页面' });
      return;
    }

    reconnectAttempts++;
    const delay = reconnectDelay * Math.pow(2, reconnectAttempts - 1); // 指数退避

    console.log(`${delay}ms后尝试第${reconnectAttempts}次重连...`);
    reconnectTimer = setTimeout(() => {
      connect();
    }, delay);
  }

  function cleanup() {
    if (heartbeatTimer) {
      clearTimeout(heartbeatTimer);
      heartbeatTimer = null;
    }
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }
  }

  function disconnect() {
    cleanup();
    reconnectAttempts = maxReconnectAttempts; // 阻止自动重连
  }

  // 初始连接
  connect();

  return {
    disconnect,
    reconnect: () => {
      cleanup();
      reconnectAttempts = 0;
      connect();
    },
    isConnected: () => eventSource?.readyState === EventSource.OPEN
  };
}
```

**文件**: `web/src/service/api/index.ts`

在现有导出中添加：
```typescript
export * from './auth';
export * from './route';
export * from './system-manage';
export * from './strm';  // 确保现有的STRM API也被导出
export * from './monitor';  // 新增
```

#### 3.2 类型定义
**文件**: `web/src/typings/api.d.ts`

在现有的 Api namespace 中添加 Monitor namespace：
```typescript
namespace Monitor {
  /** 应用健康状态数据（基于现有功能扩展） */
  interface AppHealthData {
    app_info: {
      name: string;
      version: string;
      start_time: string;
      uptime: number;
      status: 'healthy' | 'warning' | 'error';
    };
    database: {
      status: 'connected' | 'disconnected';
      size: number;
      connection_pool: {
        active: number;
        idle: number;
        total: number;
      };
      query_performance: {
        avg_response_time: number;
        slow_queries: number;
      };
    };
    strm_tasks: {  // 替代background_tasks
      status: 'healthy' | 'warning' | 'error';
      total_tasks: number;
      running_tasks: number;
      failed_tasks: number;
      success_rate: number;
      recent_failures: number;
    };
    api_performance: {  // 替代task_monitor
      status: 'healthy' | 'warning' | 'error';
      avg_response_time: number;
      request_count_24h: number;
      error_rate: number;
      slowest_endpoints: Array<{
        endpoint: string;
        avg_time: number;
        count: number;
      }>;
    };
  }

  /** 业务统计数据（基于现有STRM任务和用户系统） */
  interface BusinessStatsData {
    strm_tasks: {
      total: number;
      completed: number;
      failed: number;
      running: number;
      pending: number;
      success_rate: number;
      avg_processing_time: number;
      recent_tasks: Array<{
        id: number;
        status: string;
        created_at: string;
        processing_time?: number;
      }>;
    };
    user_activity: {
      online_users: number;
      total_users: number;
      login_count_today: number;
      active_users_week: number;
      api_requests_today: number;
    };
    file_processing: {
      uploaded_files: number;
      processed_files: number;
      storage_used: number;
      avg_file_size: number;
      file_types_distribution: Record<string, number>;
    };
  }

  /** 性能监控数据（基于APILoggerMiddleware） */
  interface PerformanceData {
    api_performance: {
      avg_response_time: number;
      request_count: number;
      error_rate: number;
      requests_per_minute: number;
      slowest_endpoints: Array<{
        endpoint: string;
        method: string;
        avg_time: number;
        count: number;
        error_count: number;
      }>;
    };
    database_performance: {
      connection_pool: {
        active: number;
        idle: number;
        total: number;
      };
      query_stats: {
        avg_query_time: number;
        slow_queries: number;
        total_queries: number;
      };
    };
  }

  /** 系统资源概览 */
  interface SystemOverviewData {
    memory: {
      total: number;
      used: number;
      available: number;
      percent: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      percent: number;
    };
    cpu: {
      percent: number;
      cores: number;
      load_avg: number[];
    };
    network: {
      bytes_sent: number;
      bytes_recv: number;
      connections: number;
    };
  }

  /** 错误分析数据 */
  interface ErrorAnalysisData {
    error_summary: {
      total_errors: number;
      error_rate: number;
      top_errors: Array<{
        type: string;
        count: number;
        percentage: number;
      }>;
    };
    failed_tasks: {
      total: number;
      by_type: Array<{
        task_type: string;
        count: number;
        main_reasons: string[];
      }>;
    };
    trend_data: Array<{
      timestamp: string;
      error_count: number;
      success_count: number;
    }>;
  }

  /** 错误分析查询参数 */
  interface ErrorAnalysisParams {
    start_date?: string;
    end_date?: string;
    error_type?: string;
  }

  /** 实时监控数据流 */
  interface RealtimeStreamData {
    timestamp: string;
    type: 'health' | 'performance' | 'business' | 'error';
    data: any;
  }
}
```

### 第四阶段：UI组件和图表实现 (2-3天)

#### 4.1 主页面布局
**文件**: `web/src/views/monitor/dashboard/index.vue`

使用 Naive UI 组件实现响应式布局（参考现有home页面结构）：
- `NGrid` 网格布局（已在项目中使用）
- `NCard` 卡片容器（已在项目中使用）
- `NSpace` 间距控制（已在项目中使用）
- 参考 `web/src/views/home/<USER>

#### 4.2 图表组件开发（基于现有ECharts配置）

**应用健康概览** (`app-health-overview.vue`):
- 应用状态指示器（健康/警告/错误）
- 数据库连接状态（基于现有数据库连接检查）
- STRM任务健康状态显示（替代TaskMonitor）
- API性能健康状态（基于APILoggerMiddleware）
- 应用运行时长显示

**业务统计图表** (`business-stats.vue`):
- STRM任务成功率环形图（使用ECharts PieChart，基于StrmTask模型）
- 用户活动趋势折线图（参考home页面LineChart）
- API请求量柱状图（基于APILog模型）
- 实时业务指标卡片（参考home页面CardData）

**STRM任务监控** (`strm-task-monitor.vue`):
- 任务状态分布饼图（使用现有PieChart配置，基于StrmTask）
- 任务处理速度趋势（使用LineChart）
- 失败任务分析表格（基于StrmTask失败记录）
- 任务统计概览（替代任务队列状态）

**性能监控图表** (`performance-charts.vue`):
- API响应时间折线图（基于APILog的process_time字段）
- 请求量统计仪表盘（使用GaugeChart）
- 状态码分布柱状图（基于APILog的response_code）
- 错误率趋势图（使用LineChart）

**系统资源监控** (`system-resources.vue`)（可选功能）:
- 内存使用率仪表盘（使用ECharts GaugeChart，需要psutil）
- 磁盘空间使用（使用PictorialBarChart，需要psutil）
- CPU使用率实时图（使用LineChart，需要psutil）
- 系统信息展示（平台信息等）

#### 4.3 数据展示组件

**用户活动监控** (`user-activity.vue`):
- 用户统计概览（基于User模型）
- 用户登录活动图表（基于last_login字段）
- API使用统计（基于APILog中的用户数据）
- 活跃用户排行

**错误分析** (`error-analysis.vue`):
- API错误类型分布图（基于APILog的response_code）
- STRM任务失败趋势分析（基于StrmTask失败记录）
- 失败任务详情表格（显示StrmTask失败详情）
- 错误统计概览

**实时告警** (`real-time-alerts.vue`):
- 应用健康状态告警
- API性能阈值告警
- STRM任务失败通知
- 数据库连接异常告警

### 第五阶段：功能完善 (1天)

#### 5.1 国际化支持
**文件**: `web/src/locales/langs/zh-cn.ts`

在现有的 route 对象中添加：
```typescript
route: {
  // ... 现有路由翻译
  monitor: '系统监控',
  monitor_dashboard: '监控仪表板'
}
```

在现有的结构中添加 page.monitor 对象：
```typescript
page: {
  // ... 现有页面翻译
  monitor: {
    title: '应用监控',
    dashboard: '监控仪表板',
    appHealth: '应用健康',
    businessStats: '业务统计',
    performance: '性能监控',
    systemResources: '系统资源',
    errorAnalysis: '错误分析',
    strmTasks: 'STRM任务监控',
    userActivity: '用户活动',
    realTimeAlerts: '实时告警',
    refreshInterval: '刷新间隔',
    healthStatus: '健康状态',
    databaseStatus: '数据库状态',
    backgroundTasks: '后台任务',
    taskMonitor: '任务监控',
    apiPerformance: 'API性能',
    taskSuccess: '任务成功率',
    responseTime: '响应时间',
    errorRate: '错误率',
    onlineUsers: '在线用户',
    totalTasks: '总任务数',
    completedTasks: '已完成',
    failedTasks: '失败任务',
    systemOverview: '系统概览'
  }
}
```

**文件**: `web/src/locales/langs/en-us.ts`

添加对应的英文翻译：
```typescript
route: {
  // ... 现有路由翻译
  monitor: 'System Monitor',
  monitor_dashboard: 'Monitor Dashboard'
}

page: {
  // ... 现有页面翻译
  monitor: {
    title: 'Application Monitor',
    dashboard: 'Monitor Dashboard',
    appHealth: 'App Health',
    businessStats: 'Business Statistics',
    performance: 'Performance Monitor',
    systemResources: 'System Resources',
    errorAnalysis: 'Error Analysis',
    strmTasks: 'STRM Task Monitor',
    userActivity: 'User Activity',
    realTimeAlerts: 'Real-time Alerts',
    refreshInterval: 'Refresh Interval',
    healthStatus: 'Health Status',
    databaseStatus: 'Database Status',
    backgroundTasks: 'Background Tasks',
    taskMonitor: 'Task Monitor',
    apiPerformance: 'API Performance',
    taskSuccess: 'Task Success Rate',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',
    onlineUsers: 'Online Users',
    totalTasks: 'Total Tasks',
    completedTasks: 'Completed',
    failedTasks: 'Failed Tasks',
    systemOverview: 'System Overview'
  }
}
```

#### 5.2 实时数据更新（基于SSE）
- 主要使用SSE（Server-Sent Events）实现真正的实时更新
- 利用项目现有的 `sse-starlette` 依赖
- 备用方案：使用 `@vueuse/core` 的 `useIntervalFn` 进行轮询
- 可配置刷新间隔（默认5秒）
- 页面失焦时暂停更新
- 连接断开时自动重连机制

```typescript
// 实时数据更新Hook示例
import { ref, onMounted, onUnmounted } from 'vue';
import { createRealtimeConnection } from '@/service/api/monitor';

export function useRealtimeMonitor() {
  const isConnected = ref(false);
  const data = ref<any>(null);
  let eventSource: EventSource | null = null;

  const connect = () => {
    eventSource = createRealtimeConnection((newData) => {
      data.value = newData;
      isConnected.value = true;
    });
  };

  const disconnect = () => {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
      isConnected.value = false;
    }
  };

  onMounted(connect);
  onUnmounted(disconnect);

  return { data, isConnected, connect, disconnect };
}
```

#### 5.3 权限控制（基于现有权限系统）
- 页面访问权限：管理员权限（roles: ['R_ADMIN']）
- API接口权限验证：使用项目标准的 `DependPermission`
- 路由守卫：利用现有的路由权限控制机制
- 参考现有manage模块的权限配置

### 第六阶段：历史数据管理和聚合 (1天)

#### 6.1 数据聚合策略（基于现有组件重新设计）
**文件**: `app/core/data_aggregator.py`

```python
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from tortoise.functions import Avg, Max, Min, Count, Sum
from tortoise.expressions import Q
from tortoise.models import Model
from tortoise import fields
from app.models.system import APILog
from app.models.strm import StrmTask
from app.log import log

# 数据聚合模型（需要添加到models中）
class MonitorHourlyStats(Model):
    """小时级监控统计数据"""
    id = fields.IntField(pk=True)
    date_hour = fields.DatetimeField(description="小时时间点")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    error_count = fields.IntField(default=0, description="错误数量")
    strm_tasks_created = fields.IntField(default=0, description="创建的STRM任务数")
    strm_tasks_completed = fields.IntField(default=0, description="完成的STRM任务数")
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "monitor_hourly_stats"
        indexes = ["date_hour"]

class MonitorDailyStats(Model):
    """日级监控统计数据"""
    id = fields.IntField(pk=True)
    date = fields.DateField(description="日期")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    total_errors = fields.IntField(default=0, description="总错误数")
    strm_tasks_total = fields.IntField(default=0, description="STRM任务总数")
    strm_tasks_success_rate = fields.FloatField(default=0, description="STRM任务成功率")
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "monitor_daily_stats"
        indexes = ["date"]

class DataAggregator:
    """监控数据聚合器（基于现有APILog和StrmTask）"""

    @staticmethod
    async def aggregate_hourly_data(target_date: datetime = None):
        """聚合小时级数据"""
        if not target_date:
            target_date = datetime.now() - timedelta(hours=1)

        hour_start = target_date.replace(minute=0, second=0, microsecond=0)
        hour_end = hour_start + timedelta(hours=1)

        # 聚合API日志数据
        api_logs = await APILog.filter(
            create_time__gte=hour_start,
            create_time__lt=hour_end
        ).all()

        total_requests = len(api_logs)
        process_times = [log.process_time for log in api_logs if log.process_time]
        avg_response_time = sum(process_times) / len(process_times) if process_times else 0
        max_response_time = max(process_times) if process_times else 0
        error_count = len([log for log in api_logs if log.response_code and int(log.response_code) >= 400])

        # 聚合STRM任务数据
        strm_tasks_created = await StrmTask.filter(
            create_time__gte=hour_start,
            create_time__lt=hour_end
        ).count()

        strm_tasks_completed = await StrmTask.filter(
            end_time__gte=hour_start,
            end_time__lt=hour_end,
            status="COMPLETED"
        ).count()

        # 存储聚合数据
        await MonitorHourlyStats.create(
            date_hour=hour_start,
            total_requests=total_requests,
            avg_response_time=avg_response_time,
            max_response_time=max_response_time,
            error_count=error_count,
            strm_tasks_created=strm_tasks_created,
            strm_tasks_completed=strm_tasks_completed
        )

        log.info(f"完成 {hour_start} 的小时级数据聚合")

    @staticmethod
    async def aggregate_daily_data(target_date: datetime = None):
        """聚合日级数据"""
        if not target_date:
            target_date = datetime.now() - timedelta(days=30)

        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(days=1)

        # 从小时级数据聚合到日级数据
        daily_stats = await MonitorHourlyStats.filter(
            date_hour__gte=start_time,
            date_hour__lt=end_time
        ).annotate(
            total_requests=Sum('total_requests'),
            avg_response_time=Avg('avg_response_time'),
            max_response_time=Max('max_response_time'),
            total_errors=Sum('error_count')
        ).values(
            'total_requests',
            'avg_response_time',
            'max_response_time',
            'total_errors'
        )

        if daily_stats:
            await MonitorDailyStats.create(
                date=start_time.date(),
                total_requests=daily_stats[0]['total_requests'] or 0,
                avg_response_time=daily_stats[0]['avg_response_time'] or 0,
                max_response_time=daily_stats[0]['max_response_time'] or 0,
                total_errors=daily_stats[0]['total_errors'] or 0
            )

        log.info(f"完成 {target_date.date()} 的日级数据聚合")

    @staticmethod
    async def cleanup_old_data():
        """清理过期的原始数据"""
        # 删除超过7天的原始API日志（保留聚合数据）
        cutoff_date = datetime.now() - timedelta(days=7)
        deleted_count = await APILog.filter(created_at__lt=cutoff_date).delete()
        log.info(f"清理了 {deleted_count} 条过期的API日志记录")

        # 删除超过90天的小时级聚合数据
        cutoff_date = datetime.now() - timedelta(days=90)
        deleted_count = await MonitorHourlyStats.filter(date_hour__lt=cutoff_date).delete()
        log.info(f"清理了 {deleted_count} 条过期的小时级聚合数据")

# 数据聚合模型（需要添加到models中）
from tortoise.models import Model
from tortoise import fields

class MonitorHourlyStats(Model):
    """小时级监控统计数据"""
    id = fields.IntField(pk=True)
    date_hour = fields.DatetimeField(description="小时时间点")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    error_count = fields.IntField(default=0, description="错误数量")
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "monitor_hourly_stats"
        indexes = ["date_hour"]

class MonitorDailyStats(Model):
    """日级监控统计数据"""
    id = fields.IntField(pk=True)
    date = fields.DateField(description="日期")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    total_errors = fields.IntField(default=0, description="总错误数")
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "monitor_daily_stats"
        indexes = ["date"]

    @staticmethod
    async def aggregate_daily_data(target_date: datetime = None):
        """聚合日级数据"""
        if not target_date:
            target_date = datetime.now() - timedelta(days=1)

        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(days=1)

        # 从小时级数据聚合到日级数据
        hourly_stats = await MonitorHourlyStats.filter(
            date_hour__gte=start_time,
            date_hour__lt=end_time
        ).all()

        if hourly_stats:
            total_requests = sum(stat.total_requests for stat in hourly_stats)
            avg_response_time = sum(stat.avg_response_time for stat in hourly_stats) / len(hourly_stats)
            max_response_time = max(stat.max_response_time for stat in hourly_stats)
            total_errors = sum(stat.error_count for stat in hourly_stats)
            strm_tasks_total = sum(stat.strm_tasks_created for stat in hourly_stats)
            strm_tasks_completed = sum(stat.strm_tasks_completed for stat in hourly_stats)
            success_rate = strm_tasks_completed / strm_tasks_total if strm_tasks_total > 0 else 0

            await MonitorDailyStats.create(
                date=start_time.date(),
                total_requests=total_requests,
                avg_response_time=avg_response_time,
                max_response_time=max_response_time,
                total_errors=total_errors,
                strm_tasks_total=strm_tasks_total,
                strm_tasks_success_rate=success_rate
            )

        log.info(f"完成 {target_date.date()} 的日级数据聚合")

    @staticmethod
    async def cleanup_old_data():
        """清理过期的原始数据"""
        # 删除超过7天的原始API日志（保留聚合数据）
        cutoff_date = datetime.now() - timedelta(days=7)
        deleted_count = await APILog.filter(create_time__lt=cutoff_date).delete()
        log.info(f"清理了 {deleted_count} 条过期的API日志记录")

        # 删除超过90天的小时级聚合数据
        cutoff_date = datetime.now() - timedelta(days=90)
        deleted_count = await MonitorHourlyStats.filter(date_hour__lt=cutoff_date).delete()
        log.info(f"清理了 {deleted_count} 条过期的小时级聚合数据")

# 定时任务设置（使用APScheduler替代不存在的BgTasks）
from apscheduler.schedulers.asyncio import AsyncIOScheduler

scheduler = AsyncIOScheduler()

async def setup_data_aggregation_tasks():
    """设置数据聚合定时任务（使用APScheduler）"""

    # 每小时执行一次小时级数据聚合（聚合上一小时的数据）
    scheduler.add_job(
        DataAggregator.aggregate_hourly_data,
        "cron",
        minute=5,  # 每小时的第5分钟执行
        id="hourly_data_aggregation",
        replace_existing=True
    )

    # 每天凌晨2点执行日级数据聚合
    scheduler.add_job(
        DataAggregator.aggregate_daily_data,
        "cron",
        hour=2,
        minute=0,
        id="daily_data_aggregation",
        replace_existing=True
    )

    # 每天凌晨3点清理过期数据
    scheduler.add_job(
        DataAggregator.cleanup_old_data,
        "cron",
        hour=3,
        minute=0,
        id="cleanup_old_data",
        replace_existing=True
    )

    scheduler.start()
    log.info("数据聚合定时任务已设置（使用APScheduler）")
```

### 第七阶段：测试和优化 (1天)

#### 7.1 功能测试
- [ ] 各监控指标数据准确性（基于现有APILog和StrmTask）
- [ ] 实时更新功能正常（SSE连接稳定性）
- [ ] 响应式布局适配（参考home页面测试）
- [ ] 图表交互功能（基于现有ECharts配置）
- [ ] 权限控制有效（使用DependPermission）
- [ ] 新监控API功能正确性
- [ ] 性能数据收集准确性（基于APILog模型）
- [ ] STRM任务监控准确性（基于StrmTask模型）
- [ ] 数据聚合功能正常（APScheduler定时任务）

#### 7.1.1 API测试用例
```python
# tests/test_monitor_api.py
import pytest
from fastapi.testclient import TestClient

async def test_app_health_endpoint():
    """测试应用健康状态API"""
    response = client.get("/api/v1/monitor/app-health")
    assert response.status_code == 200
    data = response.json()
    assert "app_info" in data["data"]
    assert "database" in data["data"]
    assert "strm_tasks" in data["data"]  # 替代background_tasks
    assert "api_performance" in data["data"]  # 替代task_monitor

async def test_business_stats_endpoint():
    """测试业务统计API"""
    response = client.get("/api/v1/monitor/business-stats")
    assert response.status_code == 200
    data = response.json()
    assert "strm_tasks" in data["data"]
    assert "user_activity" in data["data"]
    assert "api_requests" in data["data"]

async def test_performance_data_endpoint():
    """测试性能监控API"""
    response = client.get("/api/v1/monitor/performance")
    assert response.status_code == 200
    data = response.json()
    assert "api_performance" in data["data"]
    assert "database_performance" in data["data"]

async def test_system_overview_endpoint():
    """测试系统资源概览API"""
    response = client.get("/api/v1/monitor/system-overview")
    assert response.status_code == 200
    # 如果psutil未安装，应该返回错误信息
    data = response.json()
    assert "data" in data

async def test_realtime_stream_endpoint():
    """测试实时数据流API"""
    response = client.get("/api/v1/monitor/realtime-stream")
    assert response.status_code == 200
```

#### 7.2 性能优化
- 图表懒加载（参考现有home页面实现）
- 数据缓存机制（MonitorCache避免重复API调用）
- 防抖处理（实时更新频率控制）
- 内存泄漏检查（ECharts实例正确销毁）
- 监控功能对主业务性能影响最小化
- SSE连接管理优化（断线重连、连接池）
- 数据聚合优化（定时任务减少实时查询压力）

#### 7.3 用户体验优化
- 加载状态提示
- 错误处理和重试（特别是psutil不可用时的降级处理）
- 数据刷新动画
- 移动端适配
- 可选功能的优雅降级（系统资源监控）

## 📁 文件清单

### 需要创建的文件

**后端文件**:
- `app/api/v1/monitor.py` - 应用监控API接口
- `app/core/performance_analyzer.py` - 基于APILog的性能分析器
- `app/core/monitor_cache.py` - 监控数据缓存管理器
- `app/core/data_aggregator.py` - 数据聚合器（使用APScheduler）
- `app/schemas/monitor.py` - 监控数据模型
- `tests/test_monitor_api.py` - 监控API测试用例

**前端文件**:
- `web/src/views/monitor/dashboard/index.vue` - 监控仪表板主页面
- `web/src/views/monitor/dashboard/modules/*.vue` - 监控组件模块
- `web/src/service/api/monitor.ts` - 监控API服务

### 需要修改的文件

**后端文件**:
- `app/api/v1/__init__.py` - 添加监控路由
- `pyproject.toml` - 添加psutil（可选）和apscheduler依赖
- `app/models/system/__init__.py` - 添加聚合数据模型导出
- `app/core/init_app.py` - 初始化定时任务

**前端文件**:
- `web/src/router/elegant/routes.ts` - 添加监控路由
- `web/src/locales/langs/zh-cn.ts` - 添加中文翻译
- `web/src/locales/langs/en-us.ts` - 添加英文翻译
- `web/src/typings/api.d.ts` - 添加Monitor类型定义
- `web/src/service/api/index.ts` - 导出monitor API

## ⏱️ 开发时间安排

| 阶段 | 内容 | 预估时间 | 负责人 |
|------|------|----------|--------|
| 第一阶段 | 后端API开发（基于现有组件重新设计） | 2-3天 | 后端开发 |
| 第二阶段 | 前端路由和页面结构 | 1天 | 前端开发 |
| 第三阶段 | 前端服务层开发（含SSE管理） | 1.5天 | 前端开发 |
| 第四阶段 | UI组件和图表实现 | 2-3天 | 前端开发 |
| 第五阶段 | 功能完善（国际化、权限） | 1天 | 全栈开发 |
| 第六阶段 | 历史数据管理和聚合（APScheduler） | 1天 | 后端开发 |
| 第七阶段 | 测试和优化 | 1天 | 测试+开发 |
| **总计** | | **10-12天** | |

## 🔧 技术要点

### 后端技术要点（修订版）
1. **应用监控优先**: 重点监控应用业务指标和健康状态
2. **基于现有组件**: 充分利用APILoggerMiddleware、StrmTask、User等实际存在的组件
3. **重新设计架构**: 移除对不存在组件的依赖，基于APILog和StrmTask重新设计
4. **数据库查询优化**: 监控统计查询避免影响业务性能，使用数据聚合减少实时查询
5. **缓存策略**: 使用MonitorCache对统计数据进行缓存，减少重复计算
6. **异步处理**: 使用async/await处理监控数据收集
7. **权限控制**: 使用项目标准的DependPermission进行权限验证
8. **实时通信**: 利用现有sse-starlette实现真正的实时监控
9. **定时任务**: 使用APScheduler替代不存在的BgTasks进行数据聚合
10. **优雅降级**: 系统资源监控作为可选功能，psutil不可用时优雅降级

### 前端技术要点
1. **业务导向设计**: 界面设计突出业务关键指标
2. **图表库策略**: 主要使用项目现有的ECharts，VChart作为特殊图表补充
3. **布局参考**: 参考现有home页面的布局模式和组件结构
4. **实时更新**: 优先使用SSE实现真正实时更新，轮询作为备用方案
5. **响应式设计**: 适配不同设备，支持移动端查看
6. **告警机制**: 异常状态及时提醒用户
7. **国际化支持**: 完整的中英文翻译
8. **权限集成**: 使用现有路由权限控制机制

### 性能考虑（优化版）
1. **监控影响最小化**: 监控功能不能影响主业务性能（目标<3%）
2. **内存数据收集**: 使用PerformanceCollector内存收集，避免频繁文件I/O
3. **智能缓存策略**: 多层缓存（内存缓存+防缓存击穿），减少重复计算
4. **数据聚合**: 后端数据聚合+定时任务处理历史数据
5. **分级加载**: 重要数据优先加载，详细数据按需获取
6. **SSE连接优化**: 心跳机制+指数退避重连+连接池管理
7. **历史数据管理**: 自动聚合+过期清理，控制数据增长
8. **图表优化**: 基于现有ECharts配置+懒加载+内存管理

### 关键优化亮点
1. **零文件I/O性能监控**: 完全基于内存的性能数据收集
2. **生产级SSE连接**: 心跳检测+自动重连+错误恢复
3. **智能数据生命周期**: 原始数据→小时聚合→日聚合→自动清理
4. **防缓存击穿**: 异步锁机制确保高并发下的缓存一致性
5. **渐进式数据保留**: 近期高精度+远期低精度的存储策略

## 📋 验收标准

### 功能验收（修订版）
- [ ] **应用健康监控**：正确显示数据库、STRM任务、API性能状态
- [ ] **新监控架构**：基于现有组件的监控功能正常工作
- [ ] **业务统计**：准确显示STRM任务、用户活动、API请求统计（基于现有模型）
- [ ] **性能监控**：基于APILog的API响应时间、数据库性能指标正常显示
- [ ] **错误分析**：API错误统计、STRM任务失败分析功能完整
- [ ] **系统资源**：可选的系统资源监控功能，psutil不可用时优雅降级
- [ ] **实时更新**：SSE连接稳定，关键指标实时更新，刷新间隔可配置
- [ ] **图表交互**：基于现有ECharts配置的图表缩放、悬停、数据筛选
- [ ] **响应式布局**：参考home页面，在不同设备上显示正常
- [ ] **权限控制**：使用DependPermission的权限验证正常
- [ ] **国际化**：中英文翻译完整
- [ ] **数据聚合**：APScheduler定时任务正常运行，数据聚合功能正常

### 性能验收（修订版）
- [ ] **监控开销**：监控功能对主业务性能影响 < 5%
- [ ] **页面性能**：监控页面加载时间 < 3秒（参考home页面性能）
- [ ] **数据更新**：SSE实时数据更新延迟 < 2秒
- [ ] **内存使用**：前端内存使用稳定，ECharts实例正确销毁，无明显泄漏
- [ ] **查询优化**：监控相关数据库查询不影响业务查询，数据聚合减少实时查询压力
- [ ] **连接管理**：SSE连接稳定，断线重连正常
- [ ] **缓存效果**：MonitorCache有效减少重复计算，提升响应速度
- [ ] **定时任务**：APScheduler定时任务稳定运行，不影响主应用性能

### 业务价值验收（修订版）
- [ ] **问题发现**：能够及时发现应用异常和性能问题（基于APILog和StrmTask分析）
- [ ] **业务洞察**：提供有价值的STRM任务和用户活动数据
- [ ] **运维支持**：为运维决策提供数据支撑
- [ ] **用户体验**：界面直观，信息层次清晰（参考home页面设计）
- [ ] **告警机制**：异常状态能够及时提醒
- [ ] **监控体系建立**：建立完整的应用监控体系，弥补现有监控功能的不足

## 📚 参考资料

- [psutil官方文档](https://psutil.readthedocs.io/)
- [ECharts官方文档](https://echarts.apache.org/zh/index.html)
- [Naive UI组件库](https://www.naiveui.com/)
- [Vue 3官方文档](https://cn.vuejs.org/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [SSE-Starlette文档](https://github.com/sysid/sse-starlette)
- [VueUse文档](https://vueuse.org/)

## 📋 修改总结（重大修订）

### 主要修改点
1. **架构重新设计**：移除对不存在组件（TaskMonitor、BgTasks）的依赖
2. **基于现有组件**：重新设计基于APILog、StrmTask、User等实际存在的模型
3. **定时任务方案**：使用APScheduler替代不存在的BgTasks
4. **性能分析重构**：基于现有APILog模型重新设计性能分析器
5. **缓存机制优化**：实现MonitorCache减少重复查询
6. **优雅降级**：系统资源监控作为可选功能，psutil不可用时优雅降级
7. **数据聚合策略**：设计基于现有数据的聚合方案
8. **开发时间调整**：由于架构调整，总时间从8-10天调整为10-12天

### 技术优势
- 基于项目实际情况，避免依赖不存在的组件
- 充分利用现有APILoggerMiddleware和数据模型
- 保持项目技术一致性
- 提供可选功能的优雅降级方案
- 建立完整的监控数据生命周期管理

### 风险控制
- 移除了对虚假组件的依赖，避免开发阻塞
- 提供了备用方案和降级策略
- 基于实际存在的数据源，确保功能可实现

---

**文档版本**: v3.0（重大修订）
**创建日期**: 2025-01-31
**最后更新**: 2025-08-05
**状态**: 已修订，基于项目实际情况重新设计，待开发
